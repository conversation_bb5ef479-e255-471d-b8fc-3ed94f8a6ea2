# is-decimal

[![Build][build-badge]][build]
[![Coverage][coverage-badge]][coverage]
[![Downloads][downloads-badge]][downloads]
[![Size][size-badge]][size]

Check if a character is decimal.

## Install

[npm][]:

```sh
npm install is-decimal
```

## Use

```js
var decimal = require('is-decimal')

decimal('0') // => true
decimal('9') // => true
decimal('a') // => false
decimal('💩') // => false
```

## API

### `decimal(character|code)`

Check whether the given character code (`number`), or the character code at the
first position (`string`), is decimal.

## Related

*   [`is-alphabetical`](https://github.com/wooorm/is-alphabetical)
*   [`is-hexadecimal`](https://github.com/wooorm/is-hexadecimal)
*   [`is-whitespace-character`](https://github.com/wooorm/is-whitespace-character)
*   [`is-word-character`](https://github.com/wooorm/is-word-character)

## License

[MIT][license] © [<PERSON>][author]

<!-- Definitions -->

[build-badge]: https://img.shields.io/travis/wooorm/is-decimal.svg

[build]: https://travis-ci.org/wooorm/is-decimal

[coverage-badge]: https://img.shields.io/codecov/c/github/wooorm/is-decimal.svg

[coverage]: https://codecov.io/github/wooorm/is-decimal

[downloads-badge]: https://img.shields.io/npm/dm/is-decimal.svg

[downloads]: https://www.npmjs.com/package/is-decimal

[size-badge]: https://img.shields.io/bundlephobia/minzip/is-decimal.svg

[size]: https://bundlephobia.com/result?p=is-decimal

[npm]: https://docs.npmjs.com/cli/install

[license]: license

[author]: https://wooorm.com
