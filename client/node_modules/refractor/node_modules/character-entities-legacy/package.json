{"name": "character-entities-legacy", "version": "1.1.4", "description": "HTML legacy character entity information", "license": "MIT", "keywords": ["html", "entity", "entities", "character", "reference", "name", "replacement"], "repository": "wooorm/character-entities-legacy", "bugs": "https://github.com/wooorm/character-entities-legacy/issues", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "main": "index.json", "files": ["index.json"], "dependencies": {}, "devDependencies": {"bail": "^1.0.0", "browserify": "^16.0.0", "concat-stream": "^2.0.0", "prettier": "^1.0.0", "remark-cli": "^7.0.0", "remark-preset-wooorm": "^6.0.0", "tape": "^4.0.0", "tinyify": "^2.0.0", "xo": "^0.25.0"}, "scripts": {"generate": "node build", "format": "remark . -qfo && prettier --write \"**/*.js\" && xo --fix", "build-bundle": "browserify index.json -s characterEntitiesLegacy -o character-entities-legacy.js", "build-mangle": "browserify index.json -s characterEntitiesLegacy -p tinyify -o character-entities-legacy.min.js", "build": "npm run build-bundle && npm run build-mangle", "test-api": "node test", "test": "npm run generate && npm run format && npm run build && npm run test-api"}, "prettier": {"tabWidth": 2, "useTabs": false, "singleQuote": true, "bracketSpacing": false, "semi": false, "trailingComma": "none"}, "xo": {"prettier": true, "esnext": false, "ignores": ["character-entities-legacy.js"]}, "remarkConfig": {"plugins": ["preset-wooorm"]}}