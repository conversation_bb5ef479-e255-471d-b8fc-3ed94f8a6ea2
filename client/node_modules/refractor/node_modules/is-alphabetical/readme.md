# is-alphabetical

[![Build][build-badge]][build]
[![Coverage][coverage-badge]][coverage]
[![Downloads][downloads-badge]][downloads]
[![Size][size-badge]][size]

Check if a character is alphabetical.

## Install

[npm][]:

```sh
npm install is-alphabetical
```

## Use

```js
var alphabetical = require('is-alphabetical')

alphabetical('a') // => true
alphabetical('B') // => true
alphabetical('0') // => false
alphabetical('💩') // => false
```

## API

### `alphabetical(character|code)`

Check whether the given character code (`number`), or the character code at the
first position (`string`), is alphabetical.

## Related

*   [`is-decimal`](https://github.com/wooorm/is-decimal)
*   [`is-hexadecimal`](https://github.com/wooorm/is-hexadecimal)
*   [`is-alphanumerical`](https://github.com/wooorm/is-alphanumerical)
*   [`is-whitespace-character`](https://github.com/wooorm/is-whitespace-character)
*   [`is-word-character`](https://github.com/wooorm/is-word-character)

## License

[MIT][license] © [Titus Wormer][author]

<!-- Definitions -->

[build-badge]: https://img.shields.io/travis/wooorm/is-alphabetical.svg

[build]: https://travis-ci.org/wooorm/is-alphabetical

[coverage-badge]: https://img.shields.io/codecov/c/github/wooorm/is-alphabetical.svg

[coverage]: https://codecov.io/github/wooorm/is-alphabetical

[downloads-badge]: https://img.shields.io/npm/dm/is-alphabetical.svg

[downloads]: https://www.npmjs.com/package/is-alphabetical

[size-badge]: https://img.shields.io/bundlephobia/minzip/is-alphabetical.svg

[size]: https://bundlephobia.com/result?p=is-alphabetical

[npm]: https://docs.npmjs.com/cli/install

[license]: license

[author]: https://wooorm.com
