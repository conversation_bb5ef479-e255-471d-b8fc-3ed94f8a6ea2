# is-hexadecimal

[![Build][build-badge]][build]
[![Coverage][coverage-badge]][coverage]
[![Downloads][downloads-badge]][downloads]
[![Size][size-badge]][size]

Check if a character is hexadecimal.

## Install

[npm][]:

```sh
npm install is-hexadecimal
```

## Use

```js
var hexadecimal = require('is-hexadecimal')

hexadecimal('a') // => true
hexadecimal('0') // => true
hexadecimal('G') // => false
hexadecimal('💩') // => false
```

## API

### `hexadecimal(character|code)`

Check whether the given character code (`number`), or the character code at the
first position (`string`), is hexadecimal.

## Related

*   [`is-alphabetical`](https://github.com/wooorm/is-alphabetical)
*   [`is-alphanumerical`](https://github.com/wooorm/is-alphabetical)
*   [`is-decimal`](https://github.com/wooorm/is-decimal)
*   [`is-whitespace-character`](https://github.com/wooorm/is-whitespace-character)
*   [`is-word-character`](https://github.com/wooorm/is-word-character)

## License

[MIT][license] © [Titus Wormer][author]

<!-- Definitions -->

[build-badge]: https://img.shields.io/travis/wooorm/is-hexadecimal.svg

[build]: https://travis-ci.org/wooorm/is-hexadecimal

[coverage-badge]: https://img.shields.io/codecov/c/github/wooorm/is-hexadecimal.svg

[coverage]: https://codecov.io/github/wooorm/is-hexadecimal

[downloads-badge]: https://img.shields.io/npm/dm/is-hexadecimal.svg

[downloads]: https://www.npmjs.com/package/is-hexadecimal

[size-badge]: https://img.shields.io/bundlephobia/minzip/is-hexadecimal.svg

[size]: https://bundlephobia.com/result?p=is-hexadecimal

[npm]: https://docs.npmjs.com/cli/install

[license]: license

[author]: https://wooorm.com
