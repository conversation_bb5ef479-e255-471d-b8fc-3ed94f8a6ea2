{"name": "is-hexadecimal", "version": "1.0.4", "description": "Check if a character is hexadecimal", "license": "MIT", "keywords": ["string", "character", "char", "code", "hexadecimal"], "repository": "wooorm/is-hexadecimal", "bugs": "https://github.com/wooorm/is-hexadecimal/issues", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "files": ["index.js"], "dependencies": {}, "devDependencies": {"browserify": "^16.0.0", "nyc": "^15.0.0", "prettier": "^1.0.0", "remark-cli": "^7.0.0", "remark-preset-wooorm": "^6.0.0", "tape": "^4.0.0", "tinyify": "^2.0.0", "xo": "^0.25.0"}, "scripts": {"format": "remark . -qfo && prettier --write \"**/*.js\" && xo --fix", "build-bundle": "browserify . -s isHexadecimal -o is-hexadecimal.js", "build-mangle": "browserify . -s isHexadecimal -p tinyify -o is-hexadecimal.min.js", "build": "npm run build-bundle && npm run build-mangle", "test-api": "node test", "test-coverage": "nyc --reporter lcov tape test.js", "test": "npm run format && npm run build && npm run test-coverage"}, "prettier": {"tabWidth": 2, "useTabs": false, "singleQuote": true, "bracketSpacing": false, "semi": false, "trailingComma": "none"}, "xo": {"prettier": true, "esnext": false, "ignores": ["is-hexadecimal.js"]}, "nyc": {"check-coverage": true, "lines": 100, "functions": 100, "branches": 100}, "remarkConfig": {"plugins": ["preset-wooorm"]}}